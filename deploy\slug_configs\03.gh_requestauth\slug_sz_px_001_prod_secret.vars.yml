---
# secret
project_name: gh_requestauth_secret

GUNICORN_PORT: 19544

ENABLE_NONCE_CHECK: true
ENABLE_DEVICEID_CHECK: true
ENABLE_DATE_CHECK: true
ENABLE_TOKEN_CHECK: true
ENABLE_SIGN_CHECK: true

NONCE_host: 127.0.0.1
NONCE_port: 6379
NONCE_password: "ooF4mie4oes.ae1I"  # nosec
NONCE_db: 7
NONCE_prefix: "GHRA:nonce"

DEVICEID_host: HK-MG-001
DEVICEID_port: 6379
DEVICEID_password: "{{Redis.HK_MG_001.password}}"
DEVICEID_db: 2
DEVICEID_prefix: "GHDEVICE"

TOKEN_host: HK-MG-001
TOKEN_port: 6379
TOKEN_password: "{{Redis.HK_MG_001.password}}"
TOKEN_db: 5
TOKEN_prefix: "GHSSC"

LOCALCACHE_host: "127.0.0.1"
LOCALCACHE_port: 6379
LOCALCACHE_password: "ooF4mie4oes.ae1I"
LOCALCACHE_db: 0
LOCALCACHE_prefix: "localcache"

LOCALCACHE_timeout: 7200

EXPIRED_host: HK-MG-001
EXPIRED_port: 6379
EXPIRED_password: "{{Redis.HK_MG_001.password}}"
EXPIRED_db: 6
EXPIRED_prefix: "CACHE:EXPIRED:GHSSC"

APPID_SECRET_DICT_JSON: '{"Ge6aiquoo":"shaNu6quaiB8ahgh5vahnieL","uiNoh4chaesha":"oo4jaitiSais1ahc1","h5News20211029":"9AFJESS9kqpiVPHPPW30NeB","h5OpenAccount20211029":"ChaFsx8ZFa3GJZzhf7"}'

GUNICORN_WORKERS: 4
