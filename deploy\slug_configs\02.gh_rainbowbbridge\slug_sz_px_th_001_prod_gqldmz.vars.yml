---
port: 56349
project_name: gh_rainbowbridge_gqldmz_ayers-th

debug: false

# --------------- 是否是dmz网关----------------------------------
is_gqldmz: true

gh_sdk_ayers_host: ""
gh_sdk_ayers_trade_host: ""
enable_ayers: true
check_bodyhash: false
allow_gql_introspection: false
enable_global_extend_session_expire: false

gh_sdk_session_host: http://HK-PX-001:40417
gh_sdk_user_host: "http://HK-PX-001:44645"
gh_sdk_userstudio_host: "http://HK-MG-001:35366"
gh_sdk_device_host: "http://HK-PX-001:45942"
gh_sdk_trade_host: "http://HK-TD-001:17001"
gh_sdk_datahouse_host: "http://HK-MG-001:21001"
gh_sdk_datahouse_imm_nspace: "/api/v1/ghm/datahouse/immigrant_investor"

# 结构化产品生命周期服务
GH_SDK_STRUCTURE_PRODUCTS: "https://lifecycle.igoldhorse.com"

#java业务中台
# 中台服务
gh_sdk_bmp_host: "http://SZ-OPACC-001:8105"
# 中台lpoa
gh_sdk_bmp_lpoa_nspace: "customer_lpoa_api"
gh_sdk_openaccount_host: "http://SZ-OPACC-001:8105"
gh_sdk_foreign_exchange_rate: "{{gh_sdk_openaccount_host}}"
gh_sdk_foreign_exchange_application: "{{gh_sdk_openaccount_host}}"
# 风险评测问卷host
gh_sdk_risk_assess_host: "{{gh_sdk_openaccount_host}}"
# EDDA授权与入金
GH_SDK_EDDA_HOST: "http://SZ-OPACC-001:8105"
GH_SDK_EDDA_NSPACE: "hs_treasury_app"
# 百度查询ip location接口密钥
GH_BAIDU_AK: ""
GH_BAIDU_SK: ""
enable_ip_check: true
# 高德地图查询ip location接口密钥
GH_GAODE_KEY: ""
# 出金
GH_SDK_WITHDRAWAL_CASH_HOST: "http://SZ-OPACC-001:8105"
GH_SDK_WITHDRAWAL_CASH_NSPACE: "customer_withdrawal_api"

# 行情
gh_sdk_quot_common_host: "http://127.0.0.1:8100"
gh_sdk_quot_mgr_host: "http://SZ-CAL-001:8031"
#增加一个变量与common_a_host区分开中华通标志使用
gh_sdk_quot_a_host: "http://SZ-CSC-001:8020"
gh_sdk_quot_delay_a_host: "http://SZ-CSC-003:8043"
# gh_sdk_quot_common_a_host: "http://SZ-CSC-001:8020"
gh_sdk_quot_common_a_host: ""
gh_sdk_quot_common_delay_a_host: ""
gh_sdk_quot_common_hk_host: "http://SZ-CAL-001:8021"
gh_sdk_quot_common_delay_hk_host: "http://SZ-CSC-003:8043"
gh_sdk_quot_common_us_host: "http://SZ-CAL-001:8022"
gh_sdk_quot_common_delay_us_host: "http://SZ-CSC-003:8043"
default_delay_quot: true
gh_sdk_quot_common_count_host: "http://SZ-PUSH-001:8000"
gh_sdk_quot_option_host: "http://SZ-TASK-001:8027"
gh_sdk_quot_stocklist_host: "http://SZ-TASK-001:8028"
# 消息中心host, 发送邮件，短信等
gh_sdk_msg_center_host: "http://SZ-CAL-001:8029"
# 结构化行情
gh_sdk_quot_struct_host: "http://SZ-OPACC-001:8060"
# 行情套餐
gh_sdk_quot_package_host: "http://SZ-CAL-001:8031"

# 收盘价接口
gh_sdk_closing_price_host: ""
gh_sdk_closing_price_nspace: ""

# 资讯, oms
gh_sdk_quot_news_host: "http://SZ-NEWS-001:8202"
gh_sdk_help_center_host: "http://SZ-NEWS-001:8251"
help_center_url_prefix: "/oms-center-service/help_center/help_api"
# 启用OMS控制app发行版本
enable_oms_app_version_control: true
gh_sdk_oms_notice_host: "http://SZ-NEWS-001:8251"

# -----------------特性相关配置--------------------------------------------
gtd_order_max_days : 90

quotation_url: "wss://sz.quot.igoldhorse.cn/ws"
gqlsecret_url: "https://core.trade.igoldhorse.cn/secret/api/v1/gql/"
# ---------------lpoa gqlsecret网关地址---------------
lpoa_gqlsecret_url: "https://gqlsecret.lpoa.igoldhorse.cn/api/v1/gql/"
# gqlsecret_url: "https://base.trade.igoldhorse.cn/secret/api/v1/gql/"
guest_quotation_url: "wss://sz.noquot.igoldhorse.cn/ws"
currency_url: "https://currency.igoldhorse.com"

prepare_open_account: "https://opacc.igoldhorse.com"
privacy_page: "https://filescfcdn.fwdev.top/public/privacy_page_uat.html"
tos_page: "https://filescfcdn.fwdev.top/public/tos_page_uat.html"
deposit_page: "https://filescfcdn.fwdev.top/public/deposit_page_uat.html"
news_detail_page: "https://h5news.igoldhorse.cn/index.html?params={base64Param}/#/news-detail"

help_center_asset_movements_page: "https://help.igoldhorse.com/cn/help/10017/subCategory/53"
help_center_page: "https://help.igoldhorse.com"

h5_news_app_id: "h5News20211029"
h5_news_app_secret: "9AFJESS9kqpiVPHPPW30NeB"
#h5_news_app_api_url: "https://base.trade.tanghui.press/secret/api/v1/gql/"
h5_news_app_api_url: "https://core.trade.igoldhorse.cn/secret/api/v1/gql/"
#h5_news_share_api_url: "https://base.trade.tanghui.press/secret/api/v1/gql/"
h5_news_share_api_url: "https://core.trade.igoldhorse.cn/secret/api/v1/gql/"

h5_open_account_app_id: "h5OpenAccount20211029"
h5_open_account_app_secret: "ChaFsx8ZFa3GJZzhf7"
h5_open_account_app_api_url: "https://core.trade.igoldhorse.cn/secret/api/v1/gql/"
# h5_open_account_app_api_url: "https://base.trade.igoldhorse.cn/secret/api/v1/gql/"
h5_open_account_share_api_url: "https://core.trade.igoldhorse.cn/secret/api/v1/gql/"
# h5_open_account_share_api_url: "https://base.trade.igoldhorse.cn/secret/api/v1/gql/"

# 注销金马号H5页面
log_out_page: "https://acclogout.igoldhorse.com"
risk_assess_page: "https://fundrisk.igoldhorse.com"
risk_assess_result: "https://fundrisk.igoldhorse.com/reviewResults"
# 私募基金H5页面
private_equity_page: "https://fund.igoldhorse.com"
IMMIGRANT_PAGE: "https://immigrant.igoldhorse.com"
# 资讯banner跳转活动页面
meeting_activity_page: "https://meetingactive.igoldhorse.com"
about_golden_horse_page: "https://cdn.static.igoldhorse.cn/static_pages/aboutus/index.html"
# 固定票息提示
coupon_tips_page: "https://help.igoldhorse.com/cn/help/10104/subCategory/60"

# 雪球派息票据提示
scn_tips: "https://help.igoldhorse.com/cn/help/10105/subCategory/61"

# # PI认证
pi_auth: "https://pi-auth.igoldhorse.com"

# 衍生品问卷
derivatives_questionnaire: "https://derivative-risk.igoldhorse.com"

# 衍生品问卷提示
derivatives_questionnaire_tips: "https://cdn.static.igoldhorse.cn/static_pages/derivative.html"

derivative_risk: "https://derivative-risk.igoldhorse.com/part_1"

# 累积期权
about_accumulator: "https://help.igoldhorse.com/cn/help/10106/subCategory/62"

# 累沽期权
about_decumalator: "https://help.igoldhorse.com/cn/help/10107/subCategory/63"

# 风险揭示书
risk_disclosure: "https://fcn-pages.igoldhorse.com"

# 结构化下单常见问题
structure_doqa: "https://help.igoldhorse.com/cn/help/10108/subCategory/65"

# 换汇记录, LPOA
currency_ex_record: "https://lpoa-currency.igoldhorse.com"

# 基金持仓明细
fund_holding_details: "https://fund.igoldhorse.com/position"

# van
van_info: ""

# 结构化产品拼单页面
SPLICING_STRUCT_PAGE: ""

android_latest_version_name: "V1.1.000"
android_latest_version_code: ""
android_require_version_name: ""
android_require_version_code: ""
android_force_update: false
android_release_note: "【重磅】货币兑换\\n【新增】版本更新提示\\n【新增】废单原因展示\\n【优化】行情支持搜索新股\\n【优化】资产分析优化、资讯推送优化"
android_ad_tips: "诚邀您体验新版本，一键升级更新！"
android_download_url: "https://cdn.static.igoldhorse.com/app-downloads/igoldhorse.com/android-latest.apk"

ios_latest_version_name: "V1.1.000"
ios_latest_version_code: ""
ios_require_version_name: ""
ios_require_version_code: ""
ios_force_update: false
ios_release_note: "【重磅】货币兑换\\n【新增】版本更新提示\\n【新增】废单原因展示\\n【优化】行情支持搜索新股\\n【优化】资产分析优化、资讯推送优化"
ios_ad_tips: "诚邀您体验新版本，一键升级更新！"
ios_download_url: "https://apps.apple.com/cn/app/goldhorse金马/id1609550128"

webtrade_latest_version_name: ""
webtrade_latest_version_code: ""
webtrade_require_version_name: ""
webtrade_require_version_code: ""
webtrade_release_note: ""
webtrade_download_url: ""

# edda入金h5
edda_deposit: "https://security.igoldhorse.com/currency"

# edda资金记录
edda_fundrecord: "https://security.igoldhorse.com/record"

# 入金记录
withdraw_currency: ""

# -----------------lpoa相关配置--------------------------------------------
gh_sdk_lpoa_host: ""

gh_goldenkey_host: "http://SZ-PX-001:10004"
gh_goldenkey_nspace: "/api/v1/gh/gk/services"

# 配置不用行情协议弹窗的金马号
NOT_QUOT_PROMPT_LIST: []
