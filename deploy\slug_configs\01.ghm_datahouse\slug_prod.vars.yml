---
port: 21001
DEBUG: false

# Hy Message Queue Config
hy_mq_host: "mr-rabbitmq.vpn.fwdev.top"
hy_mq_port: "25674"
hy_mq_username: "rabbitmq"
hy_mq_password: ".A921120z."
hy_mq_queue_name: "JMprodpushtest"

# Server Redis Config
redis_host: HK-MG-001
redis_port: 6379
redis_password: "{{Redis.HK_MG_001.password}}"

# Redis To Get Token
ght_app_executor_redis_server: '{{redis_host}}'
ght_app_executor_redis_port: '{{redis_port}}'
ght_app_executor_redis_password: '{{redis_password}}'
ght_app_executor_redis_db: 9

#
datahouse_messager_host: '{{redis_host}}'
datahouse_messager_port: '{{redis_port}}'
datahouse_trade_messager_channel: trade
datahouse_relogin_messager_channel: relogin
datahouse_messager_password: '{{redis_password}}'

# Message Queue To Push Message
# TODO: fix configs
mqtt_broker_host: "SZ-PUSH-001"
mqtt_broker_port: 1883
mqtt_datahouse_client_id: "mqtt_client_datahouse"
mqtt_username: "mqtt"
mqtt_password: "{{MqPassword.SZ_PUSH_001.mqtt}}"

# Hy Api Config
# TODO: check hy api host
hy_brokerside_host: "hyprod.fwdev.top"
hy_brokerside_port: "443"
hy_brokerside_protocol: "https"
hy_brokerside_group: "g"
hy_brokerside_method: "POST"
hy_brokerside_body_lang: "JSON"
hy_brokerside_server_name: "hsglobal.UFG30.brokerside"
hy_brokerside_version: "v"
hy_brokerside_api_type: "brokerside"
hy_brokerside_timeout: 10

# Hy Operator Config
hy_sdk_operator_no: "admin"
hy_sdk_op_password: "Abc123456."

# HY UAT MYSQL CONFIG
# TODO: fix host
hy_mysql_host: prod-mysql.vpn.fwdev.top
hy_mysql_port: 43062
hy_mysql_user: jmzq_query
hy_mysql_pwd: jmzq@123
hy_mysql_charset: utf8

# Postgresql Config
pstg_host: HK-MG-001
pstg_port: 5432
pstg_user: "ghm_datahouse"
pstg_pwd: "{{DbPassword.HK_MG_001.ghm_datahouse}}"
db_pool_size: 100
db_pool_recycle_timeout: 60 * 60 * 2
db_echo: true

ghm_clientform_draft_redis_server: '{{redis_host}}'
ghm_clientform_draft_redis_port: '{{redis_port}}'
ghm_clientform_draft_redis_password: '{{redis_password}}'
ghm_clientform_draft_redis_db: 12
ghm_clientform_draft_redis_db_prefix: "GHM:CLIENTFORM_DRAFT:"

# Statements url configs
hy_statement_url_perfix: "https://filescfcdn.fwdev.top/public/hy_report/"
ayers_statement_url_perfix: "https://ayers-reports.igoldhorse.com/"

# Base Publish Message Configs
base_api_host: "http://SZ-CAL-001:8029"

# User Studio Configs
user_studio_host: "http://HK-MG-001:35366"
user_studio_nspace: "/gh/api/user_studio"

# USE CLIENT 使用的第三方客户端 ayers - ayers,hy - 恒云
use_client: ayers

COMMON_SERVICE_HOST : "http://SZ-CAL-001:8029"
JVM_API_NSPACE: "/platform_api"

DATAHOUSE_MESSAGER_DB_FX : 4
#Fx_transfer消息的mq配置
FX_MQ_HOST : "HK-TD-002"
FX_MQ_PORT : 5672
FX_MQ_USERNAME : "bussiness_rbtmq"
FX_MQ_PASSWORD : "{{MqPassword.HK_TD_002.fxmqpasswd}}"
FX_MQ_QUEUE_NAME : "FOREIGN_EXCHANGE_RESULT_CALLBACK"
FX_VIR_HOST : "/bussiness"
FX_MQ_QUEUE_STATE_NAME : "FOREIGN_EXCHANGE_REPORT_CALLBACK"


RBMQ_HOST : "HK-MG-002"
RBMQ_PORT : 5672
RBMQ_USERNAME : "rbtmqadmin"
RBMQ_PASSWORD : "{{MqPassword.HK_MG_002.amqppasswd}}"
RBMQ_QUEUE_NAME : "AYERS.NOTIFY.DATAHOUSE"
RBMQ_VIR_HOST : "/"
RBMQ_EXCH_NAME: "AYERS.NOTIFY"

AYERS_SERVER_HOST : "http://HK-MG-001:31412"

HK_MKT_HOST : "http://HK-MG-002:8021/mktinfo_hkex_api/get_history_quot"
US_MKT_HOST : "http://HK-MG-002:8022/mktinfo_us_api/get_history_quot"
A_MKT_HOST : ""


# MINIO CONFIGS
MINIO_ENDPOINT: "files.ayers.igoldhorse.cn"
MINIO_SECURE: True
AYERS_READ_ACCESS_KEY: "ayers-read"
AYERS_READ_SECRET_KEY: "{{MinioPassword.HK_MG_002.ayers_read_pass}}"
MINIO_BUCKET: "ayers"

# 是否打印SQL执行时间
ENABLE_SQL_ANSLYSIS: false

# FAKE AYERS CONFIG
AYERSGTS_FAKE_HOST: "http://HK-TD-001:10006"

#资产特殊处理用户
ASSET_RESTRICTED_USER: ['100029.001', '100168.001']

# 客户信息登记接收邮件帐户
ACCEPT_USER_INFO_EMAILS: ['<EMAIL>','<EMAIL>', '<EMAIL>', '<EMAIL>']