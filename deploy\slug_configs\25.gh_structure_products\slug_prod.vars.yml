server_port: 35560
debug: False
default_user_id: "66667"
process_workers: 4
ENABLE_PROFILE: False
ENABLE_MEMORY_ANALYSIS: False

# EXTRAMILE CONFIGS
extramile_host: ""
extramile_appid: 6
extramile_appsecret: ""

# DATABASE CONFIGS
pg_psy_url: "postgresql+psycopg2://gh_structure_lifecycle:{{DbPassword.HK_MG_001.gh_structure_lifecycle}}@HK-MG-001:5432/gh_structure_lifecycle"
pg_asy_url: "postgresql+asyncpg://gh_structure_lifecycle:{{DbPassword.HK_MG_001.gh_structure_lifecycle}}@HK-MG-001:5432/gh_structure_lifecycle"
pg_echo: True

# 一壶LIFECYCLE CONFIGS
api_callback: "https://lifecycle.igoldhorse.com/lifecycle/callback/"
yihu_service_nspace: "lifecycle"
yihu_service_host: "https://lcmapi.easyview.com.hk:8443"
ew_api_source: "GOLDHORSE_SECURITIES"
enable_ev_transfer_ins: True

# 中台API
BMP_SERVICE_HOST: "http://HK-TD-002:8107"

# 监听中台订单状态推送topic
BMP_ORDER_STATUS_PUSH: []

# 监听中台旧路径订单topic, 会推送订单创建和订单状态更新消息
BMP_OLD_ORDER_PUSH: ["ev_fbf4f43980e640efaac85608ffd4cc40", "ev_1440e11e7955438d8f0b4b276e4f685b", "ev_a831041b50d04421bca77ae74e34f5f9", "ev_6ec988f0b2c54ef39e500e83f8d1b43d"]


# ghm_search行情API
ghm_search_service_host: "http://HK-MG-001:10003"

# RABBITMQ CONFIGS
RBMQ_HOST: "HK-TD-002"
RBMQ_PORT: 5672
RBMQ_USERNAME: "bussiness_rbtmq"
RBMQ_PASSWORD: "{{MqPassword.HK_TD_002.fxmqpasswd}}"
RBMQ_CONNECT_SUB_BMP_LIFECYCLE_QUEUE: "LIFECYCLE_MESSAGE_QUEUE"
RBMQ_CONNECT_PUB_BMP_LIFECYCLE_QUEUE: "LIFECYCLE_OBSERVE_MESSAGE_QUEUE"
RBMQ_SUB_BMP_LIFECCYLE_SETTLE_MSG_TYPE: "CONFIRM_LIFECYCLE_NOTICE"
RBMQ_SUB_BMP_LIFECCYLE_CREATE_MSG_TYPE: "CONFIRM_STRUCT_ORDER"
RBMQ_SUB_BMP_LIFECCYLE_UPDATE_MSG_TYPE: "STRUCT_ORDER_EDIT_ISIN"
RBMQ_PUB_BMP_LIFECCYLE_NOTICE_MSG_TYPE: "LIFECYCLE_NOTICE"
RBMQ_SUB_BMP_LIFECYCLE_TSFILE_MSG_TYPE: "EDIT_LIFECYCLE_FILE"
RBMQ_VIR_HOST: "/bussiness"

# Redis Configs
REDIS_HOST: "HK-MG-001"
REDIS_DB: 13
REDIS_PASSWORD: "{{Redis.HK_MG_001.password}}"

# app消息推送sdk
APP_PUSH_MSG_HOST: "http://SZ-CAL-001:8029"

# gh_session服务
GH_SESSION_HOST: "http://HK-PX-001:40417"

# MQTT CONFIGS
MQTT_USERNAME: "push_quotation"
MQTT_PASSWORD: "{{ MqPassword.HK_TD_002.pushpassword }}"
MQTT_HOST: "HK-TD-002"
MQTT_PORT: "1883"
MQTT_CLIENTID_PREFIX: "lifecycle_mqtt_producer"

# MINIO CONFIGS:
MINIO_ENDPOINT: "files.opacc.igoldhorse.cn"
MINIO_READER_ACCESS_KEY: "biz-platform_readonly"
MINIO_READER_SECRET_KEY: "{{MinioPassword.HK_TD_002.structure_read_pass}}"
MINIO_OBJECT_URL_EXPIRE: 1800
MINIO_REPLACE_URL_PREFIX: True
MINIO_SECURE: True
MINIO_SECURE_FILE_PREFIX: "https://files.opacc.igoldhorse.cn"

# MONITOR CONFIGS 
MONITOR_INS_IDS: "tiger_brokers,tiger_brokers_hk"

# EASYVIEW TRADE CONFIGS
EV_MQTT_PREFIX: "wss"
EV_API_HOST: "https://openapi.easyview.com.hk"
EV_MQTT_HOST: "mqtt-openapi-ws.easyview.com.hk"
EV_MQTT_PORT: 15675
EV_MQTT_WS_PATH: "/ws"
EV_MQTT_USER: ""
EV_MQTT_PWD: ""
EV_APPID: "goldhorse_securities"
EV_APPSECRET: "b61631a908954d1abeaa31af0ae71221"
EV_KEY: "8tMkFQrHlSO14mQl"
EV_IV: "lBQbhXhvi9iqvVZF"
EV_TRADE_TOPIC_IN: ""
EV_TRADE_TOPIC_OUT: ""
EV_LCM_TOPIC_OUT: ""
EV_LCM_NAMESPACE: "easyview-trade-api/lifeCycle/v1"

# 托管行id
CUSTODIAN_ID: 67

# DINGTALK CONFIGS
DINGTALK_TOKEN: ""
ENABLE_DINGTALK_ROBOT: False
DINGTALK_SECRET: ""

# 将 easyview 的 issuerId 映射为 业务中台的 issuerId
ISSUER_ID_MAP: {}
