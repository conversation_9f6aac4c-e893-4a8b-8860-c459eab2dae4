debug: False
server_host: 0.0.0.0
server_port: 38001
enable_fake_sms: False
web_address: "https://lpoa.igoldhorse.com"
workers: 4

# POSTGRESQL CONFIGS
pg_psy_url: "postgresql+psycopg2://gh_lpoa_user:{{DbPassword.HK_MG_001.gh_lpoa_user}}@HK-MG-001:5432/gh_lpoa_user"
pg_asy_url: "postgresql+asyncpg://gh_lpoa_user:{{DbPassword.HK_MG_001.gh_lpoa_user}}@HK-MG-001:5432/gh_lpoa_user"

# LOCOL REDIS CONFIGS
redis_host: "HK-MG-001"
redis_port: 6379
redis_db: 11
redis_password: "{{Redis.HK_MG_001.password}}"

# SMS SDK CONFIGS
sms_sdk_host: "http://SZ-CAL-001:8029"
enable_check_sms: True

# GH_SESSION CONFIGS
gh_session_host: "http://HK-PX-001:40417"

# GHM_USERSTUDIO CONFIGS
ghm_userstudio_host: "http://HK-MG-001:35366"
ghm_userstudio_nspace: "/gh/api/user_studio"

# MINIO CONFIGS:
minio_endpoint: "files.ayers.igoldhorse.cn"
minio_public_endpoint: "files.ayers.igoldhorse.cn"
minio_writer_access_key: "lpoa_wr"
minio_writer_secret_key: "{{MinioPassword.HK_MG_002.lpoa_wr_pass}}"
minio_reader_access_key: "lpoa_readonly"
minio_reader_secret_key: "{{MinioPassword.HK_MG_002.lpoa_read_pass}}"
minio_bucket: "lpoa"
minio_secure: True
minio_object_url_expire: 1800 # 30 minutes
minio_file_url_prefix: "https://files.ayers.igoldhorse.cn"
# 客户委托协议文件在minio路径，例如 /lpoa_user/client_agreement/交易号.pdf
MINIO_ENTRUST_FILE_PATH_PREFIX: "/lpoa_user/client_agreement/%s.pdf" 
# 客户文件在minio路径
minio_client_file_path_prefix: "/lpoa_user/files/%s/%s"


# EMAIL SDK CONFIGS
email_service_host: "http://SZ-CAL-001:8029"

# GH MQTT CONFIGS
gh_rmq_host: "SZ-PUSH-001"
gh_rmq_port: 1883
gh_rmq_username: "prodmqtt"
gh_rmq_password: "{{MqPassword.SZ_PUSH_001.prodmqtt}}"
gh_rmq_client_id_prefix: "gh_lpoa_user"

# ENCRYPT CONFIGS
GHRSA_GH_LOGIN_KEYNAME: "GHRSALPOA"
GHRSA_GH_LOGIN_PRIVATE_KEY_B64: "{{GhRsa.lpoa_user.private_key_b64}}"
GHRSA_GH_LOGIN_PRIVATE_PASSPHRASE: "{{GhRsa.lpoa_user.passphrase}}"

# CAPTCHA CONFIGS
ENABLE_CAPTCHA: True
TENCENTCLOUD_SECRET_ID: '{{TencentCloud.Captcha.SecretId}}'
TENCENTCLOUD_SECRET_KEY: '{{TencentCloud.Captcha.SecretKey}}'
TX_SDK_CAPTCHA_APPID: 193868404
TX_SDK_CAPTCHA_APPSECRETKEY: "5YhzyxGPR5PIwR9q2jnYngqmi"

# SQL_ANSLYSIS CONFIGS
ENABLE_SQL_ANSLYSIS: false

# RABBITMQ CONFIGS
RBMQ_HOST: "HK-TD-002"
RBMQ_PORT: 5672
RBMQ_USERNAME: "bussiness_rbtmq"
RBMQ_PASSWORD: "{{MqPassword.HK_TD_002.fxmqpasswd}}"
RBMQ_QUEUE_NAME: "LPOA_UPDATE_CALLBACK"
RBMQ_VIR_HOST: "/bussiness"

#启动维护，不能登录交易号和下单,0不开启,1读取MAINTENANCE_TIME,2读取redis的MAINTENANCE_TIME_RDS的值
#格式类似这样[['2023-01-05 10:12:00', '2023-01-05 12:12:00'],['2023-01-05 12:12:00', '2023-01-05 13:12:00']]
ENABLE_SYSTEM_MAINTENANCE : 0
MAINTENANCE_TIME : [
    ['2023-07-12 18:00:00', '2023-07-12 19:20:00'],
    ['2023-07-13 12:00:00', '2023-07-13 12:30:00'],
    ['2023-07-14 18:00:00', '2023-07-14 19:20:00'],
]
MAINTENANCE_BLANK_LIST : ['C007', '201234.001']

# BMP LPOA CONFIGS
bmp_lpoa_host: "http://HK-TD-002:8105"
bmp_lpoa_nspace: "/customer_lpoa_api/"

# GHM_DATAHOUSE CONFIGS
GHM_DATAHOUSE_HOST: "http://HK-MG-001:21001"

# GHM_ETL CONFIGS
GHM_ETL_HOST: "http://HK-MG-001:21000"
