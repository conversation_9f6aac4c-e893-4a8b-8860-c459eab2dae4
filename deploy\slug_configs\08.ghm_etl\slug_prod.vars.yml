---
# Postgresql Config
pstg_host: HK-MG-001
pstg_port: 5432
pstg_user: "ghm_datahouse"
pstg_pwd: "{{DbPassword.HK_MG_001.ghm_datahouse}}"

# SQLALCHEMY CONFIG
db_echo: false

# Hy Mysql Config
hy_mysql_host: prod-mysql.vpn.fwdev.top
hy_mysql_port: 43062
hy_mysql_user: jmzq_query
hy_mysql_pwd: jmzq@123
hy_mysql_charset: utf8

ayers_daily_statements_dir: /data/PDFs/reports/ayers_reports/daily
ayers_monthly_statements_dir: /data/PDFs/reports/ayers_reports/monthly
ayers_statements_dir: /data/PDFs/reports/ayers_reports

sync_all_report: false
sleep_after_sync_report: 60
enable_sync_data: false

REDIS_PUB_HOST: "HK-MG-001"
REDIS_PUB_PORT: 6379
REDIS_PUB_CHANNEL_AYERS: "ayers_notify"
REDIS_PUB_PASSWORD: "{{Redis.HK_MG_001.password}}"
REDIS_PUB_DB: 13

AYERS_SERVER_HOST: "http://HK-MG-001:31412"
AYERSGTS_HOST: "http://HK-TD-001:54362"
USERSTUDIO_HOST: "http://HK-MG-001:35366"
USERSTUDIO_GH_USER_NSPACE: "/gh/api/user_studio"

DEBUG: false

GHM_SEARCH_HOST: "http://HK-MG-001:10003"

# MINIO CONFIGS
AYERS_WRITE_ACCESS_KEY: "ayers-write"
AYERS_WRITE_SECRET_KEY: "{{MinioPassword.HK_MG_002.ayers_write_pass}}"
MINIO_ENDPOINT: "HK-MG-002:42924"
MINIO_SECURE: False
MINIO_BUCKET: "ayers"

# java消息中心服务相关配置
JM_EMAIL_SERVICE_HOST: "http://SZ-CAL-001:8029"
JM_PLATFORM_SERVICE_NSPACE: "/platform_api"

# gh_session服务
GH_SESSION_HOST: "http://HK-PX-001:47846"

# LPOA服务
LPOA_USER_HOST: "http://HK-MG-001:38001"
LPOA_USER_NSPACE: "/api/lpoa/user/services"

RBMQ_HOST : "HK-MG-002"
RBMQ_PORT : 5672
RBMQ_USERNAME : "rbtmqadmin"
RBMQ_PASSWORD : "{{MqPassword.HK_MG_002.amqppasswd}}"
RBMQ_QUEUE_NAME : "AYERS.NOTIFY.ETL"
RBMQ_VIR_HOST : "/"
RBMQ_EXCH_NAME: "AYERS.NOTIFY"

JAVA_MID_FUND : "http://HK-TD-002:8105"

STATEMENTS_HOST: ""
