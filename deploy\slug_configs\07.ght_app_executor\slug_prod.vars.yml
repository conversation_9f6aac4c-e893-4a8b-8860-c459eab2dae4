---
server_port: 37201

# HY config
hy_brokerside_host: "hyprod.fwdev.top"
hy_brokerside_port: "443"
hy_brokerside_protocol: "https"
hy_license_str_brokerside: "31332261FA828E3EAF39B7F3D87520F463AAC1B9D53B58"
hy_operator_no: "admin"
hy_op_password: "Abc123456."
hy_op_station: "FE00DDE9298310CDFEEFE69229B8DB248534710F"

# Sqlalchemy config
db_uri: "postgresql+psycopg2://ght_app_executor:{{DbPassword.HK_MG_001.ght_app_executor}}@HK-MG-001:5432"

# Redis config
redis_password: "{{Redis.HK_MG_001.password}}"
redis_server: HK-MG-001
ght_app_executor_redis_password: '{{redis_password}}'
ght_app_executor_redis_server: '{{redis_server}}'

# Logging Config
log_level: "INFO"
