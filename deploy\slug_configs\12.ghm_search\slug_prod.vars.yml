deployhost: stage_servers
# Server Configs
debug: false
server_port: 10003
project_name: ghm_search
user_id_default: 66667

# QUOTES MQTT CONFIGS
mqtt_broker: HK-MG-002
mqtt_port: 1883
mqtt_user: prodmqtt
mqtt_pwd: "{{MqPassword.SZ_PUSH_001.prodmqtt}}"
enable_mqtt: False

# LOCOL REDIS CONFIGS
redis_host: HK-MG-001
redis_port: 6379
redis_db: 4
redis_password: "{{Redis.HK_MG_001.password}}"

# QUOTES MARKET SERVICE CONFIGS
quotes_hk_service_host: http://HK-MG-002:8021
quotes_us_service_host: http://HK-MG-002:8022
quotes_a_service_host: http://HK-MG-002:8020
quotes_hk_delay_service_host: "http://HK-MG-002:8043"
quotes_us_delay_service_host: "http://HK-MG-002:8043"
quotes_a_delay_service_host: "http://HK-MG-002:8043"

redis_cache_open: False

userstudio_host: "http://HK-MG-001:35366"

# 美股价格返回分析
enable_us_price_return_analysis: True

# 是否替换特殊市场股票代码中的空格为点
replace_special_space: True
