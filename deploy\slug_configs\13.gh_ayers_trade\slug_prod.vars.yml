deployhost: stage_servers
server_port: 10002
debug: False

# Api Configs
ayers_gts_host: "http://HK-TD-001:54362"
ayers_gts_lpoa_host: "http://HK-TD-003:54363"


# USERSTUDIO CONFIGS
gh_sdk_userstudio_host: "http://HK-MG-001:35366"

# SESSION CONFIGS
session_host: "http://HK-PX-001:40417"

trade_limit_host: "http://HK-MG-001:35001"
enable_trade_limit: False

NO_EXTENDED_TRADE: []

# 临时添加, 交易时间限制开关
ENABLE_US_TRADE_TIME_LIMIT: True

ALL_CHECK_US_TRADE_TIME : True
ID_CHECK_US_TRADE_TIME: ['100001.001', '200055.001', '100003.001', '200003.001']
CHECK_US_TRADE_HALF_DAY : True


#vip相关功能
#vip美股开关
VIP_ENABLE_US : True
#vip户口列表

VIP_TRADE_ACCOUNT_LIST : [
  '100011.001',
  '100003.001',
  ]
#vip是否针对所有股票
VIP_ENABLE_ALL_STOCK : False
#只针对特定股票，需要VIP_ENABLE_ALL_STOCK为False才生效
VIP_STOCK_LIST : ['F']
#买入方向限定，为[]则不限定
VIP_TRADE_BS : ['B', 'S']
#是否只针对限价单
VIP_ENABLE_ONLY_LIMIT_ORDER : True
#vip下单逻辑的服务地址
VIP_TRADE_HOST : 'http://HK-TD-001:10006'
#vip拒絕接單時間區間
VIP_ENABLE_PLACE_TIME : [[[0, 0, 0], [8, 0, 0]]]

#钉钉机器人
DD_PUSH_URL: "https://oapi.dingtalk.com/robot/send?access_token=7f038738e3cc3fae34ac5dce33b37646c62fbadca89f1414df02739d934e09a9"
DD_SECRET: "SECa140cfcb946f96eec80b6a9b50c3650f01c0eb704d12468cebce76c4d2007fab"

# 港股交易限制
ENABLE_HK_TRADE_TIME_LIMIT: True
ID_CHECK_HK_TRADE_TIME: []
# A股交易限制
ENABLE_A_TRADE_TIME_LIMIT: True
ID_CHECK_A_TRADE_TIME: []
# 台股交易限制
ENABLE_TW_TRADE_TIME_LIMIT: True
ID_CHECK_TW_TRADE_TIME: []
ENABLE_TW_HOLIDAY_LIMIT: False

# DATAHOUSE SERVICE CONFIGS
DATAHOUSE_SERVICE_HOST: "http://HK-MG-001:21001"
DATAHOUSE_SERVICE_NAMESPACE: "/api/v1/ghm/datahouse"