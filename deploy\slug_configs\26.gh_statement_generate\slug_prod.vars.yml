deployhost: stage_servers


PORT: 50002
DEBUG: false
RELOAD: false
DB_ECHO: false

GET_STATEMENTS_FROM_DB: true

AYERS_SQLSERVER_DB: "io_db_green"
AYERS_SQLSERVER_USER: '{{DbPassword.GREENB_IODB.iodb_username}}'
AYERS_SQLSERVER_PASSWD: '{{DbPassword.GREENB_IODB.iodb_password}}'
AYERS_SQLSERVER_HOST: 'GREENB-IODB-PROXY'
AYERS_SQLSERVER_PORT: 14333


PG_STATEMENT_GENERATE_DB: 'gh_statement_generate'
PG_STATEMENT_GENERATE_USER: 'gh_statement_generate'
PG_STATEMENT_GENERATE_PASSWD: '{{DbPassword.HK_MG_001.gh_statement_generate}}'
PG_STATEMENT_GENERATE_HOST: 'HK-MG-001'
PG_STATEMENT_GENERATE_PORT: 5432


PG_DB_URI_ASYNC: "postgresql+asyncpg://gh_statement_generate:{{DbPassword.HK_MG_001.gh_statement_generate}}@HK-MG-001:5432/gh_statement_generate"
PG_DB_URI: "postgresql+psycopg2://gh_statement_generate:{{DbPassword.HK_MG_001.gh_statement_generate}}@HK-MG-001:5432/gh_statement_generate"


# java消息中心服务相关配置
JM_EMAIL_SERVICE_HOST: ""
# 投资移民账户费用通知邮箱
IMM_FEE_NOTICE_EMAIL: ""

RUN_IMM_MERGE_JOB: false

# api服务host
USERSTUDIO_HOST: "http://HK-MG-001:35366"

# minio config
MINIO_ENDPOINT: "files.ayers.igoldhorse.cn"
MINIO_WRITER_ACCESS_KEY: "lpoa_wr"
MINIO_WRITER_SECRET_KEY: "{{MinioPassword.HK_MG_002.lpoa_wr_pass}}"
MINIO_READER_ACCESS_KEY: "lpoa_readonly"
MINIO_READER_SECRET_KEY: "{{MinioPassword.HK_MG_002.lpoa_read_pass}}"
MINIO_BUCKET: "lpoa"
MINIO_OBJECT_URL_EXPIRE: 36000
MINIO_SECURE: True
