---
deployhost: stage_servers

DEBUG: false
RELOAD: false
DB_ECHO: false

SERVER_HOST: "0.0.0.0"
SERVER_PORT: 35366

SESSION_HOST: 'http://HK-PX-001:40417'
SESSION_PREFIX: 'gh'

PG_USERSTUDIO_DB: ghm_userstudio
PG_USERSTUDIO_USER: ghm_studio
PG_USERSTUDIO_PASSWD: "{{DbPassword.HK_MG_001.ghm_userstudio}}"
PG_USERSTUDIO_HOST: HK-MG-001
PG_USERSTUDIO_PORT: '5432'

# GH_REDIS_SERVER: HK_MG_001
GH_REDIS_SERVER: ************
GH_REDIS_PORT: 6379
GH_REDIS_PASSWORD: "{{Redis.HK_MG_001.password}}"
GH_REDIS_DB: 5

GH_USERSTUDIO_RMQ_HOST: "SZ-PUSH-001"
GH_USERSTUDIO_RMQ_PORT: 1883
GH_USERSTUDIO_RMQ_USERNAME: 'prodmqtt'
GH_USERSTUDIO_RMQ_PASSWORD: '{{MqPassword.SZ_PUSH_001.prodmqtt}}'
GH_USERSTUDIO_RMQ_CLIENT_ID_PREFIX: 'ghm_userstudio'

# MINIO CONFIGS:
minio_endpoint: "HK-MG-002:42924"
minio_writer_access_key: "lpoa_wr"
minio_writer_secret_key: "{{MinioPassword.HK_MG_002.lpoa_wr_pass}}"
minio_reader_access_key: "lpoa_readonly"
minio_reader_secret_key: "{{MinioPassword.HK_MG_002.lpoa_read_pass}}"
minio_bucket: "lpoa"
minio_secure: False
minio_object_url_expire: 1800 # 30 minutes
minio_file_url_prefix: "http://HK-MG-002:42924"
# 发送异常登录信息邮箱地址
minio_excel_email: '<EMAIL>'


# 业务中台推送风险评测状态mq
# RBMQ_HOST: "HK-MG-002"
# RBMQ_PORT: 56720
RBMQ_HOST: "SZ-OPACC-001"
RBMQ_PORT: 5672
RBMQ_USERNAME: "bussiness_rbtmq"
RBMQ_PASSWORD: "{{MqPassword.HK_TD_002.fxmqpasswd}}"
RBMQ_QUEUE_NAME: "USER_RISK_STATUS_UPDATE"
RBMQ_VIR_HOST: "/bussiness"
# 业务中台推送风险评测状态mq--香港服务器
RBMQ_HOST_HK: "HK-TD-002"
RBMQ_PORT_HK: 5672
RBMQ_USERNAME_HK: "bussiness_rbtmq"
RBMQ_PASSWORD_HK: "{{MqPassword.HK_TD_002.fxmqpasswd}}"
RBMQ_QUEUE_NAME_HK: "USER_RISK_STATUS_UPDATE"
RBMQ_VIR_HOST_HK: "/bussiness"

GHM_USERSTUDIO_AYERS_SERVICE_HOST: 'http://HK-MG-001:31412'
GH_SDK_DATAHOUSE_HOST: "http://HK-MG-001:21001"
# 行情套餐
gh_sdk_quot_package_host: "http://SZ-CAL-001:8031"

HY_SDK_OUTSIDE_HOST: "hyuat.vpn.fwdev.top"
HY_SDK_OUTSIDE_PORT: "443"
HY_SDK_OUTSIDE_GROUP: "g"
HY_SDK_OUTSIDE_METHOD: "POST"
HY_SDK_OUTSIDE_BODY_LANG: "JSON"
HY_SDK_OUTSIDE_SERVER_NAME: "hsglobal.UFG30.outside"
HY_SDK_OUTSIDE_VERSION: "v"
HY_SDK_OUTSIDE_API_TYPE: "outside"
HY_SDK_OUTSIDE_PROTOCOL: "https"
HY_SDK_OUTSIDE_TIMEOUT: 10

HY_MYSQL_HOST: "prod-mysql.vpn.fwdev.top"
HY_MYSQL_USER: "jmzq_query"
HY_MYSQL_PWD: "jmzq@123"
HY_MYSQL_PORT: 43062
HY_MYSQL_CHARSET: "utf8"

GHUSER_HOST: "http://HK-PX-001:44645"
email_service_jhost: "http://SZ-CAL-001:8029"
EMAIL_SERVICE_HOST: "http://SZ-CAL-001:8029/platform_api/send_email_form"
SMS_SERVICE_HOST: "http://SZ-CAL-001:8029/platform_api/send_msg"
JM_PLATFORM_SERVICE_NSPACE: "/platform_api"

PHONE_SMS_EX: 600
PHONE_DEVICE_2FA_PREFIX: "USERSTUDIO:PHONE:DEVICE:2FA"
PHONE_SMS_PREFIX: "USERSTUDIO:PHONE:SMS"
TRADE_CLIENTID_PREFIX: "USERSTUDIO:TRADE:CLIENTID:DEVICE"
TRADE_CLIENTID_DEVICE_PREFIX : "USERSTUDIO:TRADE:CLIENTID:LOGINDEV"
# 修改密码后记录时长，提醒用户修改密码用
TRADE_UPDATE_PASSWORD_ALERT_PREFIX: "USERSTUDIO:TRADE:UPDATE:PASSWORD:ALERT"
TRADE_UPDATE_PASSWORD_ALERTED_PREFIX: "USERSTUDIO:TRADE:UPDATE:PASSWORD:ALERTED"
# 一年 365*24*60*60
TRADE_UPDATE_PASSWORD_ALERT_EX: ********
# 一月 30*24*60*60
TRADE_UPDATE_PASSWORD_ALERTED_EX: 2592000

ENABLE_SMS_CODE_666: false

ENABLE_GH_LOGIN_SSO: true
ENABLE_TRADE_LOGIN_OTHER: true

ENABLE_REMOVE_SESSION_FOR_OPEN_ACCOUNT: true
ENABLE_DEBUG_MSG_TO_DINGTALK: false

GHRSA_GH_LOGIN_KEYNAME: "GHRSALOGIN"
GHRSA_GH_LOGIN_PRIVATE_KEY_B64: '{{GhRsa.gh_login.private_key_b64}}'
GHRSA_GH_LOGIN_PRIVATE_PASSPHRASE: '{{GhRsa.gh_login.passphrase}}'
# public key for debug usage
GHRSA_GH_LOGIN_PUBLIC_KEY_B64: 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE2SUJ3TWVCdEY1cm9ub205c2VZeApXUDhwRXFER2VIbnBIQ0FCRGF3YTRwUTBzQm9sYmF5L3dVM3BEQmFVYUgwajExRXVaaHp4THRKdWNSd0RSUEFZCmlueWlkN3RuWXFWZmI5QnpDc1M1b0NXRDBDV2tQN0Q0L01UY2RNaFZTck13Tko1RXQzUitNU2JOaGNUSjRWRSsKc2p0SXJDV1dSQTdWK2FBeGpPZmRqMUwwZXFKS25DU2xlVVFYZWlaZFJuMFlJQjI0Tmd0RStDSXQwQnhEOGNEMQpRbmwxY0lXNlkxT0FXSXBuOHVjTXp6amNhUVY5Y2FqZmwwYnZScngxYVBZL1VNUFFYOXBYekIzUWFpWjJDUDVYClYreVNuNVpEWnN5ZDdGbUg1VmJ5ZmxMS2pWUm4xb0JaWDkxcGFJQ3ZnelB5cnNsZlptYUZUVXNFU2NZMnp2cVUKRVFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=='
GHRSA2_KEYNAME: "GHRSA2"
GHRSA2_KEY_B64: '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
GHRSA2_PASSPHRASE: ''


DEBUG_LOGIN_SMS_CODE: false

TX_SDK_CAPTCHA_APPID: 2005881624
TX_SDK_CAPTCHA_APPSECRETKEY: '{{TencentCloud.Captcha.AppSecret}}'
TENCENTCLOUD_SECRET_ID: '{{TencentCloud.Captcha.SecretId}}'
TENCENTCLOUD_SECRET_KEY: '{{TencentCloud.Captcha.SecretKey}}'

TEST_PHONE_SET:
  "86":
    "18000000000": "342789"
    "18000000001": "864870"
    "18000000002": "898870"
    "18000000003": "021590"
    "18000000004": "176819"
    "18000000005": "640950"
    "***********": "277154"
    "***********": "457285"
    "***********": "655095"
    "***********": "729293"  # for apple appstore audit

ALLOW_VTYPE_PWD_PLAIN: true
ALLOW_LOGIN_PASSWORD_PLAIN: true
ALLOW_TRADE_PASSWORD_PLAIN: true

GH_LPOA_USER_HOST: "http://HK-MG-001:38001"

ENABLE_CAPTCHA: false
# 是否展示A股市场
SHOW_A_QUOT: false
# 是否允许搜索A股
SEARCH_A_QUOT: true

# 是否检测交易账号异常登录，并发送交易账号异常登录邮件通知
ENABLE_SEND_LOGIN_ABNORMAL_EMAIL: true

# 对特殊帐号不判断设备数量
NOT_CHECK_DEVICE_VALID_CLIENT_LIST: ['100011.001', '200011.001']

# 是否打印SQL执行时间
ENABLE_SQL_ANSLYSIS: false

# 轻量级服务
GH_WEBTRADE_HUB_HOST: "http://HK-PX-001:10007"
GH_WEBTRADE_HUB_SECRET_KEY: "aioeipid3"
GH_WEBTRADE_HUB_SECRET_VALUE: "xoupweipi19"

GHM_SEARCH_HOST: "http://HK-MG-001:10003"

# 提示用户修改密码天数
CHANGE_PASSWORD_ALERT: 83
# 启用redis 分布式锁
ENABLE_RED_LOCK: true

# 登录金马号时开启对越狱和root设备的拦截
# 登录交易号时开启对越狱和root设备的拦截
# 0不开启，1开启提示并拦截，2只开启拦截，3只开启提示，4提示并退出
ENABLE_COMPROMISED_START: 0
ENABLE_COMPROMISED_REG: 1
ENABLE_COMPROMISED_GH_LOGIN: 1
ENABLE_COMPROMISED_TRADE_LOGIN: 1


#启动维护，不能登录交易号和下单,0不开启,1读取MAINTENANCE_TIME,2读取redis的MAINTENANCE_TIME_RDS的值
#格式类似这样[['2023-01-05 10:12:00', '2023-01-05 12:12:00']]
ENABLE_SYSTEM_MAINTENANCE : 1
MAINTENANCE_TIME : [
    ['2025-02-17 17:45:00', '2025-02-17 20:15:00'],
]
MAINTENANCE_BLANK_LIST : ['100026.001']

# 配置不用行情协议弹窗的金马号
NOT_QUOT_PROMPT_LIST: []

# SMS LIMIT CONFIGS
SMS_LIMIT: 100

