.before_script: &before_script
- command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )
- ssh-agent -s > SSH-AGENT
- eval $(cat SSH-AGENT)
- echo "${DEPLOY_BOT_SSH_PRIVATE_KEY}" | base64 -d | tr -d '\r' | ssh-add -
- echo "${ID_ED25519_GH_PROD_GHCI_SSH_KEY}" | base64 -d -w0 | tr -d '\r' > ssh.key
- echo "${ANSIBLE_VAULT_PASSWORD_PROD_PY}" | base64 -d > "${ANSIBLE_VAULT_PASSWORD_FILE}"
- echo $(sha256sum "${ANSIBLE_VAULT_PASSWORD_FILE}")
- chmod 400 ssh.key
- mkdir -p ~/.ssh
- ssh-keyscan ${GITLAB_SSH_IP} >> ~/.ssh/known_hosts
- chmod 644 ~/.ssh/known_hosts
- echo ${ID_ED25519_GH_PROD_GHCI_SSH_PASSPHRASE} | base64 -d -w0 | tr -d '\r' > SSH_PASSPHRASE
- chmod 400 SSH_PASSPHRASE
- sshpass -f SSH_PASSPHRASE -P 'key:' ssh-add ssh.key
- export ANSIBLE_INVENTORY=hosts.ini
- export ANSIBLE_HOST_KEY_CHECKING=false
- export ANSIBLE_PIPELINING=true
- export ANSIBLE_BECOME_PASS=$(echo $ANSIBLE_BECOME_PASS_BASE64 | base64 -d -w0 | tr -d '\r')
- export PROJECT_NAME=$(echo ${GITLAB_PROJECT} | cut -d '/' -f 2 | tr 'a-z' 'A-Z')
- export PROJECT_VERSION=${PROJECT_NAME}_VERSION
- export VERSION_VALUE=$(printenv ${PROJECT_VERSION})
- echo "VERSION_VALUE = ${VERSION_VALUE:-no-version}"
- rm -rf project
- git clone -b ${VERSION_VALUE:-no-version} --depth 1 git@${GITLAB_SSH_IP}:${GITLAB_PROJECT}.git project

.deploy_script: &deploy_script
  - export SLUG=${SLUG}
  - export PROJECT_DIR=$(echo ${CI_JOB_NAME} | cut -d':' -f 1)
  - cp deploy/hosts.ini project/deploy
  - cp deploy/prod_base.vars.yml project/deploy
  - cp deploy/slug_configs/${PROJECT_DIR}/slug_${SLUG}.vars.yml project/deploy
  - cd project/deploy
  - |
    ansible-playbook \
    playbook.yml \
    -e @../../vault.vars.yml \
    -e @base.vars.yml \
    -e @slug_${SLUG}.vars.yml \
    -e @base.vars.yml \
    -e @prod_base.vars.yml \
    -e @slug_${SLUG}.vars.yml \
    -e @prod_base.vars.yml \
    -e deployhost=${deployhost} \
    -u ${ANSIBLE_USER:-ghci} \
    -b \
    ${ARGS} \
    ;

01.ghm_datahouse:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_middleground/ghm_datahouse
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script

02.gh_rainbowbbridge:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_rainbowbridge
  when: manual
  needs: []
  parallel:
    matrix:
    # SZ
    - SLUG: sz_px_th_001_prod_gqldmz
      deployhost: SZ-PX-001
    - SLUG: sz_px_th_001_prod_gqlsecret
      deployhost: SZ-PX-001
  before_script: *before_script
  script: *deploy_script
  resource_group: gh_rainbowbbridge

03.gh_requestauth:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_requestauth
  when: manual
  needs: []
  parallel:
    matrix:
    # HK
    - SLUG: prod_dmz
      deployhost: HK-PX-001
    - SLUG: prod_secret
      deployhost: HK-PX-001
    # SH
    - SLUG: sh_px_001_prod_dmz
      deployhost: SH-PX-001
    - SLUG: sh_px_001_prod_secret
      deployhost: SH-PX-001
    # SZ
    - SLUG: sz_px_001_prod_dmz
      deployhost: SZ-PX-001
    - SLUG: sz_px_001_prod_secret
      deployhost: SZ-PX-001
  before_script: *before_script
  script: *deploy_script
  resource_group: gh_requestauth

04.gh_sessions:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_sessions
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-PX-001
  before_script: *before_script
  script: *deploy_script

05.gh_users:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_users
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-PX-001
  before_script: *before_script
  script: *deploy_script

06.gh_cherrypie:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_cherrypie
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-PX-001
  before_script: *before_script
  script: *deploy_script

07.ght_app_executor:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_tradehub/ght_app_executor
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-001
  before_script: *before_script
  script: *deploy_script

08.ghm_etl:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_middleground/ghm_etl
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script

09.gh_ayers_services:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_ayers_services
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
    - SLUG: prod
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script

10.gh_ayersgts:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_ayersgts
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-001
    - SLUG: prod_lpoa
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script

11.ghm_userstudio:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_middleground/ghm_userstudio
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
    - SLUG: prod
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script

12.ghm_search:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_middleground/ghm_search
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script

13.gh_ayers_trade:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_ayers_trade
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-001
    - SLUG: prod
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script


14.gh_marketreport:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_risk_dev/gh_marketreport
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script

15.gh_datacollector:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_risk_dev/gh_datacollector
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script


16.gh_tradelimit:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_risk_dev/gh_tradelimit
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script


17.gh_lpoa_user:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_lpoa/gh_lpoa_user
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script


18.gh_lpoatrade:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_lpoa/gh_lpoatrade
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-MG-001
  before_script: *before_script
  script: *deploy_script


19.lpoa_londonbridge:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_lpoa/lpoa_londonbridge
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: SZ-PX-001
  before_script: *before_script
  script: *deploy_script
  resource_group: lpoa_londonbridge


20.gh_ayersgts_web:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_ayersgts_web
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-001
  before_script: *before_script
  script: *deploy_script


21.gh_webtrade_hub:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_webtrade_hub
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-PX-001
  before_script: *before_script
  script: *deploy_script

22.gh_goldenkey:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_goldenkey
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: SZ-PX-001
  before_script: *before_script
  script: *deploy_script

23.gh_ayersgts_fake:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_ayersgts_fake
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-001
  before_script: *before_script
  script: *deploy_script

24.gh_iodb:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_iodb
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script

25.gh_structure_products:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_structure_products
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script

26.gh_statement_generate:
  only:
  - main
  stage: prod
  tags:
  - prod-deploy
  variables:
    GITLAB_PROJECT: gh_services/gh_statement_generate
  when: manual
  needs: []
  parallel:
    matrix:
    - SLUG: prod
      deployhost: HK-TD-003
  before_script: *before_script
  script: *deploy_script
