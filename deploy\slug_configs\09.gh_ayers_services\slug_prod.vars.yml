---
deployhost: stage_servers

port: 31412
debug: false

# Api Configs
ayers_gts_host: "http://HK-TD-001:54362"

quotes_service_host: "http://HK-MG-002:8028"

calculate_quotes_data: true

ghm_userstudio_host: "http://HK-MG-001:35366"

ghm_search_host: "http://HK-MG-001:10003"

GHM_DATAHOUSE_HOST: "http://HK-MG-001:21001"

# AYERS GTS FAKE CONFIG
ayers_gts_fake_host: "http://HK-TD-001:10006"

# ETL CONFIG
ghm_etl_host: "http://HK-MG-001:21000"

DATAHOUSE_MESSAGER_HOST : "HK-MG-001"
DATAHOUSE_MESSAGER_PORT : 6379
DATAHOUSE_MESSAGER_PASSWORD : "{{Redis.HK_MG_001.password}}"
DATAHOUSE_MESSAGER_DB_FX : 4

TRADE_LIMIT_HOST: "http://HK-MG-001:35001"

JAVA_MID_FUND : "http://HK-TD-002:8105"

OPEN_CALC_DAILY_INCOME_HK : True
OPEN_CALC_DAILY_INCOME_US : True
OPEN_CALC_DAILY_INCOME_A : True

OPEN_CALC_COST: False
CALC_COST_CLIENT_LIST : []

OPEN_ENQUIRE_POSITION_CACHE: False

#打开柜台维护中，打开后登录会提示对应信息
OPEN_AYERS_MAINTAIN: False

user_id_default: "666667"

# 入金鉴权白名单
CASH_IN_CLIENT_LIST: []

# 资金冻结鉴权白名单
CASH_HOLD_CLIENT_LIST: []

# 解冻出金鉴权白名单
CASH_RELEASE_OUT_CLIENT_LIST: []

#超级变态的需求为了修正给crm的数据
SPC_LOGIC_CLIENTS: [
]

#针对特定户口修正uncleared_amt
SPC_LOGIC_CLIENTS_NEGATIVE : [
    '700029.001',
    '500009.001',
]
