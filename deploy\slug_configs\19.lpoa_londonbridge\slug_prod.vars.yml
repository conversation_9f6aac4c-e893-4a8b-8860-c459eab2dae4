port: 38000
project_name: lpoa_londonbridge
debug: false
reload: false
sdk_lpoa_trade_url: http://HK-MG-001:38002/api/v1/gh/lpoatrade/services/
sdk_lpoa_user_url: http://HK-MG-001:38001/api/lpoa/user/services/
sdk_lpoa_user_host: http://HK-MG-001:38001
sdk_lpoa_user_nspace: "/api/lpoa/user/services/"
sdk_gh_session_url: http://HK-PX-001:40417/gh/api/session/
sdk_gh_device_url: http://HK-PX-001:45942/
sdk_middleground_url: http://SZ-OPACC-001:8105/
sdk_oms_url: "http://SZ-NEWS-001:8251/"
sdk_datahouse_host: "http://HK-MG-001:21001"
sdk_datahouse_imm_nspace: "/api/v1/ghm/datahouse/immigrant_investor"
sdk_jvm_platform_api_host: "http://SZ-CAL-001:8029"
sdk_jvm_platform_api_nspace: "/platform_api"
host: 0.0.0.0

# -----------------consul相关---------------------------------------------
enable_consul: false
# 以下配置暂时用不上，所以目前留空或留uat配置都可以
CONSUL_ADDRESSES: ************:8500,************:8500,************:8500
CONSUL_TOKEN: 3129c712-9a09-bcc9-ff77-4b9c8c571197
REGISTER_TO_CONSUL_SERVER_NAME: lpoa_londonbridge
REGISTER_TO_CONSUL_SERVER_ID: lpoa_londonbridge1
REGISTER_TO_CONSUL_SERVER_HOST: ************
REGISTER_TO_CONSUL_SERVER_PORT: 38000
SERVICE_NAMES: gh_lpoa_user,gh_lpoatrade,gh_middleground
