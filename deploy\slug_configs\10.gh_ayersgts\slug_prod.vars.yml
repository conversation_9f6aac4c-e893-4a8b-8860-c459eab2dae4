---
deployhost: stage_servers

DEBUG: false

port: 54362
db_uri: 'postgresql+asyncpg://gh_ayersgts:{{DbPassword.HK_MG_001.gh_ayersgts}}@HK-MG-001:5432/gh_ayersgts'

# ayers服务地址
HY_HOST : "AYERS_GTS_API"
HY_PORT : 16868
HY_IS_ENCRYPT: true
HY_ENCRYPT_KEY: "{{AYERS.encrypt_key}}"

# 操作员配置
HY_OPERATOR_SITE: "GREEN"
HY_OPERATOR_STATION : "GREEN-010-IJJ369"
HY_OPERATOR_USER : "APPGH"
HY_OPERATOR_PASSWORD : "{{AYERS.operator_password}}"

# redis配置
REDIS_PUB_HOST: "HK-MG-001"  # "127.0.0.1"
REDIS_PUB_PORT: 6379
REDIS_PUB_CHANNEL: "trade"
REDIS_PUB_CHANNEL_RELOGIN: "relogin"
REDIS_PUB_CHANNEL_AYERS: "ayers_notify"
REDIS_PUB_PASSWORD: "{{Redis.HK_MG_001.password}}"

# 连接超时
TIME_OUT_CONNECT: 5.0
# 请求超时
TIME_OUT_REQ: 10.0

# zeromq端口配置
MQ_HOST: "127.0.0.1"
MQ_PORT_HTTP_MQA: 49002  # http->mq
MQ_PORT_HTTP_MQB: 49003  # mq->http
MQ_PORT_SOCKET_MQA: 49004  # mq->socket
MQ_PORT_SOCKET_MQB: 49005  # soctet->mq

RBMQ_HOST : "HK-MG-002"
RBMQ_PORT : 5672
RBMQ_USERNAME : "rbtmqadmin"
RBMQ_PASSWORD : "{{MqPassword.HK_MG_002.amqppasswd}}"
RBMQ_QUEUE_NAME : "AYERS.NOTIFY"
RBMQ_VIR_HOST : "/"
RBMQ_EXCH_NAME: "AYERS.NOTIFY"

OPEN_LOG_FILE : False

GTS_TCP_PROGRESS_COUNT: 3
