---
deployhost: stage_servers

DEBUG: false

port: 44649
db_uri: 'postgresql+asyncpg://gh_ayersgts:{{DbPassword.HK_MG_001.gh_ayersgts}}@HK-MG-001:5432/gh_ayersgts'


# ayers服务地址
HY_HOST : "AYERS_GTS_API"
HY_PORT : 16868
HY_IS_ENCRYPT: true
HY_ENCRYPT_KEY: "{{AYERS.encrypt_key}}"

# 操作员配置
HY_OPERATOR_SITE: "GREEN"
HY_OPERATOR_STATION : "GREEN-010-IJJ369"
HY_OPERATOR_USER : "APPGH"
HY_OPERATOR_PASSWORD : "{{AYERS.operator_password}}"

# redis配置
REDIS_PUB_HOST: "HK-MG-001"  # "127.0.0.1"
REDIS_PUB_PORT: 6379
REDIS_PUB_CHANNEL: "trade_web"
REDIS_PUB_CHANNEL_RELOGIN: "relogin_web"
REDIS_PUB_CHANNEL_AYERS: "ayers_notify_web"
REDIS_PUB_PASSWORD: "{{Redis.HK_MG_001.password}}"
REDIS_PUB_DB: 1

# 连接超时
TIME_OUT_CONNECT: 5.0
# 请求超时
TIME_OUT_REQ: 10.0

# zeromq端口配置
MQ_HOST: "127.0.0.1"
MQ_PORT_HTTP_MQA: 35732  # http->mq
MQ_PORT_HTTP_MQB: 35733  # mq->http
MQ_PORT_SOCKET_MQA: 35734  # mq->socket
MQ_PORT_SOCKET_MQB: 35735  # soctet->mq

RBMQ_HOST : "HK-MG-002"
RBMQ_PORT : 5672
RBMQ_USERNAME : "rbtmqadmin"
RBMQ_PASSWORD : "{{MqPassword.HK_MG_002.amqppasswd}}"
RBMQ_QUEUE_NAME : "AYERS.NOTIFY.WEB"
RBMQ_VIR_HOST : "/"
