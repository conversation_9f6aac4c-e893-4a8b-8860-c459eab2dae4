debug: false
reload: false
db_echo: true

port: 38002
db_uri: "postgresql+asyncpg://gh_lpoatrade:{{DbPassword.HK_MG_001.gh_lpoatrade}}@HK-MG-001:5432/gh_lpoatrade"

DB_URI2: "postgresql+psycopg2://gh_lpoatrade:{{DbPassword.HK_MG_001.gh_lpoatrade}}@HK-MG-001:5432/gh_lpoatrade"

HTTP_TIMEOUT : 10
AYERS_SERVER_HOST : "http://HK-MG-001:31412"
AYERSGTS_HOST : "http://HK-TD-001:54362"
LPOA_USER_HOST : "http://HK-MG-001:38001"
AYERS_TRADE_HOST : "http://HK-TD-001:10002"

HK_MARKET_PRICE_HOST : "http://HK-MG-002:8021/mktinfo_hkex_api/get_quot"
US_MARKET_PRICE_HOST : "http://HK-MG-002:8022/mktinfo_us_api/get_quot"

MQ_HOST: "HK-MG-002"
MQ_PORT: 5672
MQ_USERNAME: "rbtmqadmin"
MQ_PASSWORD: "{{MqPassword.HK_MG_002.amqppasswd}}"
MQ_QUEUE_NAME: "LPOA.DIV"
MQ_VIR_HOST: "/"

BACKEND_REDIS_HOST: "HK-MG-001"
BACKEND_REDIS_PORT: 6379
BACKEND_REDIS_PASSWORD: "{{Redis.HK_MG_001.password}}"
BACKEND_REDIS_DB: 7
BACKEND_NAME: "LPOA-TRADE-TASK"


ORDER_STATE_REDIS_HOST : "HK-MG-001"
ORDER_STATE_REDIS_PORT : 6379
ORDER_STATE_REDIS_CHANNEL : "trade"
ORDER_STATE_REDIS_PASSWORD : "{{Redis.HK_MG_001.password}}"
ORDER_STATE_REDIS_DB : 5


CACHE_REDIS_HOST : "HK-MG-001"
CACHE_REDIS_PORT : 6379
CACHE_REDIS_CHANNEL : "trade"
CACHE_REDIS_PASSWORD : "{{Redis.HK_MG_001.password}}"
CACHE_REDIS_DB : 7
CACHE_FUNDINFO_KEY : "LPOA-FUND-INFO"

# SQL_ANSLYSIS CONFIGS
ENABLE_SQL_ANSLYSIS: false

GH_SESSION_HOST: 'http://HK-PX-001:40417'

GH_RMQ_HOST: "SZ-PUSH-001"
GH_RMQ_PORT: 1883
GH_RMQ_USERNAME: 'prodmqtt'
GH_RMQ_PASSWORD: '{{MqPassword.SZ_PUSH_001.prodmqtt}}'
GH_RMQ_CLIENT_ID_PREFIX: 'gh_lpoatrade'

# 临时添加, 交易时间限制开关
ENABLE_CHECK_TIME: true
# 是否开启上手方下单限制
ALL_CHECK_US_TRADE_TIME: true
# 美股交易限制白名单
ID_CHECK_US_TRADE_TIME: [6]
# 半日假限制
CHECK_US_TRADE_HALF_DAY: true

#下单维护检查
ENABLE_CHECK_SYSTEM_MAINTENANCE: false

# sftp上传文件 sftp服务器端 保存目录
SFTP_UPLOAD_SERVER_DIR: "/upload/"
SFTP_SERVER_HOST: '***************'
SFTP_SERVER_PORT: 22
SFTP_SERVER_USER: 'dts029'
SFTP_SERVER_PASSWORD: 'WV8+UWXF%15Q=3r7Ji4'

# 是否开启手动sftp上传
ENABLE_MANUAL_UPLOAD_FILE: true
# 是否开启job中的上传功能
ENABLE_JOB_SFTP_UPLOAD: true

# 是否打开A股交易限制
ENABLE_CHECK_A_TIME: True
# A股交易限制是否限制所有用户
ALL_CHECK_A_TRADE_TIME: True
# A股交易限制白名单
ID_CHECK_A_TRADE_TIME: []
# 是否打开港股交易限制
ENABLE_CHECK_HK_TIME: True
# 港股交易限制是否限制所有用户
ALL_CHECK_HK_TRADE_TIME: True
# 港股交易限制白名单
ID_CHECK_HK_TRADE_TIME: []
