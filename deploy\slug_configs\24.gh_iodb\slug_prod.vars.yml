deployhost: stage_servers

DEBUG: False
DB_ECHO: False

PORT: 50001

# database
SS_USERSTUDIO_DB: 'io_db_green'
SS_USERSTUDIO_USER: '{{DbPassword.GREENB_IODB.iodb_username}}'
SS_USERSTUDIO_PASSWD: '{{DbPassword.GREENB_IODB.iodb_password}}'
SS_USERSTUDIO_HOST: 'GREENB-IODB-PROXY'
SS_USERSTUDIO_PORT: '14333'

DB_URI: "mssql+pymssql://{{SS_USERSTUDIO_USER}}:{{SS_USERSTUDIO_PASSWD}}@{{SS_USERSTUDIO_HOST}}:{{SS_USERSTUDIO_PORT}}/{{SS_USERSTUDIO_DB}}"

# redis
GH_REDIS_SERVER: "************"
GH_REDIS_PORT: 6379
GH_REDIS_PASSWORD: "{{Redis.HK_MG_001.password}}"
GH_REDIS_DB: 11

# rabbitMQ
MG_MQ_HOST: "HK-TD-002"
MG_MQ_PORT: 5672
MG_MQ_USERNAME: "bussiness_rbtmq"
MG_MQ_PASSWORD: "{{MqPassword.HK_TD_002.fxmqpasswd}}"
MG_MQ_QUEUE_NAME: "IODB_RESULT_CALLBACK"
MG_VIR_HOST: "/bussiness"

# ayers service
GH_AYERS_SERVICE_HOST: "http://HK-TD-001:54362"

# 请求时间限制
ENABLE_TIME_VALIDATE: False
