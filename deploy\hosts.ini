[all:vars]
ansible_ssh_pipelining=yes
ansible_python_interpreter=/usr/bin/python3

[not_init_user:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o PreferredAuthentications=password -o PasswordAuthentication=yes -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"'

[not_init_user]

[not_init_vdb:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"'

[not_init_vdb]

[ayers_servers]
AYS-GW-001    ansible_host=************ tags=[ubuntu,ayers_gateway]

[hk_servers]
HK-PX-001    ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,proxy]
HK-MG-001    ansible_host=************** lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ ayers_gts_api_ip=*************** tags=[ubuntu,datahouse,timescaledb,redis,jumpproxy,ayers]
HK-MG-002    ansible_host=************   lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,datahouse,prometheus,loki,alertmanager,grafana,frp_server,minio]
HK-TD-001    ansible_host=*************  lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ ayers_gts_api_ip=*************** tags=[ubuntu,trade,ayers]
HK-TD-002    ansible_host=************   lan_ip=************  nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,trade]
HK-TD-003    ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************ loki_ip=************ tags=[ubuntu,trade]

[sh_servers]
# SH
SH-PX-001    ansible_host=*************  lan_ip=************  nvpn_ip=************  tags=[ubuntu,proxy]

[sz_servers:vars]
ansible_ssh_common_args='-o StrictHostKeyChecking=no -o ProxyCommand="ssh -o StrictHostKeyChecking=no -W %h:%p -q -p 22222 ghci@************"'

[sz_servers]
SZ-TASK-001  ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-KAFKA-001 ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-KAFKA-002 ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-KAFKA-003 ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-STORE-001 ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos] ansible_python_interpreter=/usr/bin/python
SZ-STORE-002 ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos] ansible_python_interpreter=/usr/bin/python
SZ-STORE-003 ansible_host=************   lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos] ansible_python_interpreter=/usr/bin/python
SZ-REDIS-001 ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-REDIS-002 ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-REDIS-003 ansible_host=************** lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-PUSH-001  ansible_host=*************  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos,rabbitmq,minio]
SZ-ES-001    ansible_host=************   lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos,prometheus,loki,grafana,alertmanager,frp_server,jumpproxy]
SZ-CVT-001   ansible_host=************** lan_ip=172.29.80.109 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-REC-001   ansible_host=47.106.114.156 lan_ip=172.29.80.107 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-CAL-001   ansible_host=47.106.91.56   lan_ip=172.29.80.108 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[centos]
SZ-PX-001    ansible_host=47.106.95.178  lan_ip=************* nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu,proxy]
SZ-CSC-001   ansible_host=172.29.80.132  lan_ip=172.29.80.132 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-CSC-002   ansible_host=172.29.80.134  lan_ip=172.29.80.134 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-CSC-003   ansible_host=172.29.80.131  lan_ip=172.29.80.131 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-OPACC-001 ansible_host=172.29.80.133  lan_ip=172.29.80.133 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]
SZ-NEWS-001  ansible_host=172.29.80.130  lan_ip=172.29.80.130 nvpn_ip=************* minio_ip=************* loki_ip=************* tags=[ubuntu]

[hk_corp]
gh81 ansible_host=172.16.80.81 lan_ip=172.16.80.81 tags=[ubuntu]
gh82 ansible_host=172.16.80.82 lan_ip=172.16.80.82 tags=[ubuntu]

[hk_qcloud_lighthouse]
LH-FG1 ansible_host=43.132.180.193
LH-FG2 ansible_host=43.129.191.143

[local_servers]
p902 ansible_host=192.168.1.92 lan_ip=192.168.1.92 tags=[ubuntu]

[all_servers:children]
hk_servers
sz_servers
sh_servers
ayers_servers
