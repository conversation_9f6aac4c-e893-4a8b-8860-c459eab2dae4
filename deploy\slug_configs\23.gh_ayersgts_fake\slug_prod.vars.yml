deployhost: stage_servers

DEBUG: False

PORT: 10006

# database
PG_USERSTUDIO_DB: gh_ayersgts_fake
PG_USERSTUDIO_USER: gh_ayersgts_fake
PG_USERSTUDIO_PASSWD: "{{DbPassword.HK_MG_001.gh_ayersgts_fake}}"
PG_USERSTUDIO_HOST: HK-MG-001
PG_USERSTUDIO_PORT: '5432'

DB_URI: "postgresql+asyncpg://{{PG_USERSTUDIO_USER}}:{{PG_USERSTUDIO_PASSWD}}@{{PG_USERSTUDIO_HOST}}:{{PG_USERSTUDIO_PORT}}/{{PG_USERSTUDIO_DB}}"

# redis
GH_REDIS_SERVER: HK-MG-001
GH_REDIS_PORT: 6379
GH_REDIS_PASSWORD: "{{Redis.HK_MG_001.password}}"
GH_REDIS_DB: 3

# gts
AYERS_GTS_HOST: "http://HK-TD-001:54362"
# userstudio
GH_SDK_USERSTUDIO_HOST: "http://HK-MG-001:35366"
# web trade lite
AYERS_GTS_WEB_HOST: "http://HK-TD-001:44649"

# 持仓缓存
ENABLE_PORTFOLIO_CACHE: False
# 资金缓存
ENABLE_BALANCE_CACHE: False

# 请求GTS服务的的最大并发数
GTS_MAX_CONNECTION_COUNT: 30

# redis缓存
REDIS_CACHE: True

# 钉钉推送
DD_PUSH_URL: "https://oapi.dingtalk.com/robot/send?access_token=6b0b48188f2660c6d777a3831c31a8a80aaba77c7865708a796f90bd5e2d3037"
DD_SECRET: "SEC122829b47b69ad548b8696bfc0299097028239bf40ac83a4413fafa01abafa7d"

#给中台的消息队列
RBMQ_HOST : "HK-TD-002"
RBMQ_PORT : 5672
RBMQ_USERNAME : "bussiness_rbtmq"
RBMQ_PASSWORD : "{{MqPassword.HK_TD_002.fxmqpasswd}}"
RBMQ_QUEUE_NAME : "US_CUTOFF_ORDER_NOTIFY"
RBMQ_VIR_HOST : "/bussiness"


#郵件服務器
EMAIL_SERVICE_JHOST : "http://SZ-CAL-001:8029"


# datahouse 的站内推送地址
REDIS_PUB_HOST: "HK-MG-001"  # "127.0.0.1"
REDIS_PUB_PORT: 6379
REDIS_PUB_CHANNEL: "trade"
REDIS_PUB_PASSWORD: "{{Redis.HK_MG_001.password}}"
REDIS_PUB_DB : 0

# 短时间重复请求
# 短时间重复请求缓存过期时间
MOMENT_CACHE_EXPIRE : 10
# 短时间重复请求锁过期时间
MOMENT_LOCK_EXPIRE : 8
# 短时间重复请求白名单
MOMENT_WHITE_LIST : []
