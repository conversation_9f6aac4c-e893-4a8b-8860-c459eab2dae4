
DEBUG : false
port : 35001


DB_URI : "postgresql+asyncpg://gh_tradelimit:{{DbPassword.HK_MG_001.gh_tradelimit}}@HK-MG-001:5432/gh_tradelimit"

# REDIS CONFIGS
REDIS_HOST : "HK-MG-001"
REDIS_PORT : 6379
REDIS_DB : 6
REDIS_PASSWORD : "{{Redis.HK_MG_001.password}}"


HK_MARKET_PRICE_HOST: "http://HK-MG-002:8021/mktinfo_hkex_api/get_quot"
US_MARKET_PRICE_HOST: "http://HK-MG-002:8022/mktinfo_us_api/get_quot"
A_MARKET_PRICE_HOST: "http://HK-MG-002:8020/mktinfo_a_api/get_quot"

EXT_RBMQ_HOST : "HK-TD-002"
EXT_RBMQ_PORT : 5672
EXT_RBMQ_USERNAME : "bussiness_rbtmq"
EXT_RBMQ_PASSWORD : "{{MqPassword.HK_TD_002.fxmqpasswd}}"
EXT_RBMQ_QUEUE_NAME : "FOREIGN_EXCHANGE_RATE_UPDATE"
EXT_RBMQ_VIR_HOST : "/bussiness"

RBMQ_HOST : "HK-MG-002"
RBMQ_PORT : 5672
RBMQ_USERNAME : "rbtmqadmin"
RBMQ_PASSWORD : "{{MqPassword.HK_MG_002.amqppasswd}}"
RBMQ_QUEUE_NAME : "AYERS.NOTIFY.ORDER"
RBMQ_VIR_HOST : "/"


RATE_MID_HOST : "http://HK-TD-002:8105/trade_limit_api/find_current_rate_info"

OPEN_LIMIT : True
OPEN_LIMIT_ALL_CLIENT : True
